# CLAUDE.md

This file provides guidance to Claude Code (claude.ai/code) when working with code in this repository.

## Project Overview

This is the AutoAnnotation project - a new, empty repository that has not been initialized yet. The repository currently contains only Claude Code configuration files.

## Getting Started

This appears to be a fresh project directory. Common next steps would be:

1. Initialize the project structure based on the intended technology stack
2. Set up version control with `git init`
3. Create initial project files (package.json, requirements.txt, etc. depending on language)
4. Configure development tools and dependencies

## Current State

- No source code files exist yet
- No package managers (npm, pip, cargo, etc.) are configured
- Not a git repository
- Only contains Claude Code settings in `.claude/settings.local.json`

## Future Development

Once the project is initialized, this file should be updated to include:
- Build and test commands
- Project architecture details
- Development workflow instructions
- Technology stack information